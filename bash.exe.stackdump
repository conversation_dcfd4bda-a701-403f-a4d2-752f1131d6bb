Stack trace:
Frame         Function      Args
0007FFFFAB50  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9A50) msys-2.0.dll+0x1FE8E
0007FFFFAB50  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE28) msys-2.0.dll+0x67F9
0007FFFFAB50  000210046832 (000210286019, 0007FFFFAA08, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAB50  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAB50  000210068E24 (0007FFFFAB60, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAE30  00021006A225 (0007FFFFAB60, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFBF4080000 ntdll.dll
7FFBF23A0000 KERNEL32.DLL
7FFBF1A70000 KERNELBASE.dll
7FFBF2DF0000 USER32.dll
000210040000 msys-2.0.dll
7FFBF1630000 win32u.dll
7FFBF3830000 GDI32.dll
7FFBF1930000 gdi32full.dll
7FFBF1700000 msvcp_win.dll
7FFBF14E0000 ucrtbase.dll
7FFBF25F0000 advapi32.dll
7FFBF3A80000 msvcrt.dll
7FFBF39D0000 sechost.dll
7FFBF1FC0000 RPCRT4.dll
7FFBF0800000 CRYPTBASE.DLL
7FFBF1660000 bcryptPrimitives.dll
7FFBF2FD0000 IMM32.DLL
