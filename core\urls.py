from django.urls import path
from .views.auth_views import (
    RegisterView, LoginView, LogoutView, ProtectedView,
    RequestPasswordResetView, ResetPasswordView, RefreshTokenView
)
from .views.user_views import (
    User<PERSON>iew, UserList<PERSON>iew, UserDetailView, UserCreateView,
    UserUpdateView, UpdateProfileView, ChangePasswordView,
    UserPermissionsView, UserPermissionsListView, MigrateUsersView
)

from .views.team_views import (
    TeamListCreateView, TeamDetailView,
    TeamMemberView, TeamResponsableView
)
from .views.team_member_views import TeamMemberSearchView, TeamMemberCreateView, TeamMemberAddView
from .views.user_management_views import UserSearchView, UserCreateView as SuperAdminUserCreateView
from .views.event_views import (
    EventListCreateView, EventDetailView,
    EventStatusUpdateView, EventArchiveView
)
from .views.event_unarchive_views import EventUnarchiveView
from .views.personal_event_views import (
    PersonalEventListCreateView, PersonalEventDetailView
)
from .views.personal_event_archive_views import PersonalEventArchiveView
from .views.personal_event_unarchive_views import PersonalEventUnarchiveView
from .views.personal_event_archived_list_views import PersonalEventArchivedListView
from .views.personal_task_views import (
    PersonalTaskListCreateView, PersonalTaskDetailView, PersonalTaskArchiveView
)
from .views.personal_task_unarchive_views import PersonalTaskUnarchiveView
from .views.team_task_views import (
    TeamTaskListCreateView, TeamTaskDetailView, TeamTaskArchiveView,
    TeamTaskStatusUpdateView, TeamTaskBulkStatusUpdateView
)
from .views.team_task_unarchive_views import TeamTaskUnarchiveView
from .views.personal_note_views import (
    PersonalNoteListCreateView, PersonalNoteDetailView,
    PersonalNoteArchiveView, PersonalNoteUnarchiveView
)
from .views.personal_journal_views import (
    PersonalJournalListCreateView, PersonalJournalDetailView,
    PersonalJournalArchiveView, PersonalJournalUnarchiveView
)
from .views.pomodoro_views import PomodoroSettingsView, PomodoroControlView
from .views.bi_views import BiMetricsView, BiDashboardView, BiHistoricalDataView, SuperAdminDashboardView, RealTimeLoginStatsView, LoginDataDebugView, AdminDashboardView, AdminActivityDebugView, EmployeeDashboardView
from .views.debug_views import DebugEmployeesView
from .views.color_views import (
    get_color_palette_view,
    suggest_color_view,
    validate_color_view,
    get_color_categories_view
)

urlpatterns = [
    path('register/', RegisterView.as_view(), name='register'),
    path('login/', LoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path('refresh-token/', RefreshTokenView.as_view(), name='refresh-token'),
    path('protected/', ProtectedView.as_view(), name='protected'),
    path('user/', UserView.as_view(), name='user'),
    path('users/', UserListView.as_view(), name='user-list'),
    path('users/search/', UserSearchView.as_view(), name='user-search'),

    # Routes pour les événements personnels
    path('personal-events/', PersonalEventListCreateView.as_view(), name='personal-event-list-create'),
    path('personal-events/archived/list/', PersonalEventArchivedListView.as_view(), name='personal-event-archived-list'),
    path('personal-events/<str:event_id>/', PersonalEventDetailView.as_view(), name='personal-event-detail'),
    path('personal-events/<str:event_id>/archive/', PersonalEventArchiveView.as_view(), name='personal-event-archive'),
    path('personal-events/<str:event_id>/unarchive/', PersonalEventUnarchiveView.as_view(), name='personal-event-unarchive'),

    # Routes pour les tâches personnelles
    path('personal-tasks/', PersonalTaskListCreateView.as_view(), name='personal-task-list-create'),
    path('personal-tasks/<str:task_id>/', PersonalTaskDetailView.as_view(), name='personal-task-detail'),
    path('personal-tasks/<str:task_id>/archive/', PersonalTaskArchiveView.as_view(), name='personal-task-archive'),
    path('personal-tasks/<str:task_id>/unarchive/', PersonalTaskUnarchiveView.as_view(), name='personal-task-unarchive'),

    # Routes pour les tâches d'équipe
    path('team-tasks/', TeamTaskListCreateView.as_view(), name='team-task-list-create'),
    path('team-tasks/<str:task_id>/', TeamTaskDetailView.as_view(), name='team-task-detail'),
    path('team-tasks/<str:task_id>/archive/', TeamTaskArchiveView.as_view(), name='team-task-archive'),
    path('team-tasks/<str:task_id>/unarchive/', TeamTaskUnarchiveView.as_view(), name='team-task-unarchive'),
    path('team-tasks/<str:task_id>/status/', TeamTaskStatusUpdateView.as_view(), name='team-task-status-update'),
    path('team-tasks/bulk/status/', TeamTaskBulkStatusUpdateView.as_view(), name='team-task-bulk-status-update'),

    path('users/create/', SuperAdminUserCreateView.as_view(), name='super-admin-user-create'),
    path('users/<str:user_id>/', UserDetailView.as_view(), name='user-detail'),
    path('users/<str:user_id>/update/', UserUpdateView.as_view(), name='user-update'),
    path('users/<str:user_id>/permissions/', UserPermissionsView.as_view(), name='user-permissions'),
    path('permissions/list/', UserPermissionsListView.as_view(), name='permissions-list'),
    path('profile/update/', UpdateProfileView.as_view(), name='update-profile'),
    # Route unique pour le changement de mot de passe (depuis le profil)
    path('profile/change-password/', ChangePasswordView.as_view(), name='change-password'),
    path('teams/', TeamListCreateView.as_view(), name='team-list-create'),
    path('teams/<str:team_id>/', TeamDetailView.as_view(), name='team-detail'),
    path('teams/<str:team_id>/members/search/', TeamMemberSearchView.as_view(), name='team-member-search'),
    path('teams/<str:team_id>/members/create/', TeamMemberCreateView.as_view(), name='team-member-create'),
    path('teams/<str:team_id>/members/', TeamMemberAddView.as_view(), name='team-member-add'),
    path('teams/<str:team_id>/members/<str:member_id>/', TeamMemberView.as_view(), name='team-members-detail'),
    path('teams/<str:team_id>/responsable/', TeamResponsableView.as_view(), name='team-responsable'),
    path('events/', EventListCreateView.as_view(), name='event-list-create'),
    path('events/<str:event_id>/', EventDetailView.as_view(), name='event-detail'),
    path('events/<str:event_id>/status/', EventStatusUpdateView.as_view(), name='event-status-update'),
    path('events/<str:event_id>/archive/', EventArchiveView.as_view(), name='event-archive'),
    path('events/<str:event_id>/unarchive/', EventUnarchiveView.as_view(), name='event-unarchive'),
    path('password/reset-request/', RequestPasswordResetView.as_view(), name='request-password-reset'),
    path('password/reset/<str:token>/', ResetPasswordView.as_view(), name='reset-password'),
    path('migrate-users/', MigrateUsersView.as_view(), name='migrate-users'),

    # Routes pour les notes personnelles
    path('personal-notes/', PersonalNoteListCreateView.as_view(), name='personal-note-list-create'),
    path('personal-notes/<str:note_id>/', PersonalNoteDetailView.as_view(), name='personal-note-detail'),
    path('personal-notes/<str:note_id>/archive/', PersonalNoteArchiveView.as_view(), name='personal-note-archive'),
    path('personal-notes/<str:note_id>/unarchive/', PersonalNoteUnarchiveView.as_view(), name='personal-note-unarchive'),

    # Routes pour les journaux personnels
    path('personal-journals/', PersonalJournalListCreateView.as_view(), name='personal-journal-list-create'),
    path('personal-journals/<str:journal_id>/', PersonalJournalDetailView.as_view(), name='personal-journal-detail'),
    path('personal-journals/<str:journal_id>/archive/', PersonalJournalArchiveView.as_view(), name='personal-journal-archive'),
    path('personal-journals/<str:journal_id>/unarchive/', PersonalJournalUnarchiveView.as_view(), name='personal-journal-unarchive'),

    # Routes pour le mode Pomodoro
    path('pomodoro/settings/', PomodoroSettingsView.as_view(), name='pomodoro-settings'),
    path('pomodoro/control/<str:action>/', PomodoroControlView.as_view(), name='pomodoro-control'),

    # Routes pour les tableaux de bord BI
    path('bi/metrics/', BiMetricsView.as_view(), name='bi-metrics'),
    path('bi/dashboard/', BiDashboardView.as_view(), name='bi-dashboard'),
    path('bi/historical-data/', BiHistoricalDataView.as_view(), name='bi-historical-data'),
    path('bi/super-admin/dashboard/', SuperAdminDashboardView.as_view(), name='super-admin-dashboard'),
    path('bi/admin/dashboard/', AdminDashboardView.as_view(), name='admin-dashboard'),
    path('bi/employee/dashboard/', EmployeeDashboardView.as_view(), name='employee-dashboard'),
    path('bi/admin/debug/', AdminActivityDebugView.as_view(), name='admin-activity-debug'),
    path('bi/realtime/login-stats/', RealTimeLoginStatsView.as_view(), name='realtime-login-stats'),
    path('bi/debug/login-data/', LoginDataDebugView.as_view(), name='login-data-debug'),
    path('dashboard/stats', BiMetricsView.as_view(), name='dashboard-stats'),  # Route alternative pour la compatibilité

    # Route de débogage (temporaire)
    path('debug/employees/', DebugEmployeesView.as_view(), name='debug-employees'),

    # Routes pour la gestion des couleurs
    path('colors/palette/', get_color_palette_view, name='color-palette'),
    path('colors/suggest/', suggest_color_view, name='color-suggest'),
    path('colors/validate/', validate_color_view, name='color-validate'),
    path('colors/categories/', get_color_categories_view, name='color-categories'),
]