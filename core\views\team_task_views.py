from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from ..decorators import admin_required
from ..mongo_models import User, Team
from ..models.team_task_model import TeamTask
from datetime import datetime, timezone
import logging

logger = logging.getLogger(__name__)

class TeamTaskListCreateView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def post(self, request):
        """Créer une nouvelle tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            data = request.data
            user = request.user

            # Validation des données
            if not data.get('title'):
                return Response({"error": "Le titre de la tâche est requis"}, status=400)
            if not data.get('start_date') or not data.get('end_date'):
                return Response({"error": "Les dates de début et de fin sont requises"}, status=400)
            if not data.get('team_id'):
                return Response({"error": "L'ID de l'équipe est requis"}, status=400)
            if not data.get('responsable'):
                return Response({"error": "Le responsable de la tâche est requis"}, status=400)

            # Vérifier si une tâche avec le même titre existe déjà pour la même équipe
            if TeamTask.objects(title=data['title'], team_id=data['team_id']).first():
                return Response({"error": "Une tâche avec ce titre existe déjà pour cette équipe"}, status=400)

            # Vérifier que l'admin est responsable de l'équipe assignée
            try:
                team = Team.objects.get(id=data['team_id'])
                if team.responsable != str(user.id):
                    return Response({"error": "Vous n'êtes pas autorisé à créer une tâche pour cette équipe. Seul l'administrateur responsable de l'équipe peut le faire."}, status=403)
            except Team.DoesNotExist:
                return Response({"error": "Équipe non trouvée"}, status=404)
            except Exception as e:
                logger.error(f"Erreur lors de la vérification de l'équipe: {str(e)}")
                return Response({"error": "Une erreur est survenue lors de la vérification de l'équipe"}, status=500)

            # Vérifier que le responsable est un admin valide
            try:
                responsable = User.objects.get(id=data['responsable'])
                if responsable.role != 'admin':
                    return Response({"error": "Le responsable doit être un administrateur"}, status=400)
            except User.DoesNotExist:
                return Response({"error": "Responsable non trouvé"}, status=404)
            except Exception as e:
                logger.error(f"Erreur lors de la vérification du responsable: {str(e)}")
                return Response({"error": "Une erreur est survenue lors de la vérification du responsable"}, status=500)

            # Vérifier que les dates sont valides (pas dans le passé)
            try:
                start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
                end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
                now = datetime.now(timezone.utc)

                if start_date.date() < now.date():
                    return Response({"error": "La date de début ne peut pas être dans le passé"}, status=400)
                if end_date.date() < start_date.date():
                    return Response({"error": "La date de fin ne peut pas être antérieure à la date de début"}, status=400)
            except ValueError:
                return Response({"error": "Format de date invalide"}, status=400)

            # Récupérer le nom du membre si un member_id est fourni
            member_name = ""
            if data.get('member_id'):
                try:
                    from ..mongo_models import User
                    member = User.objects.get(id=data['member_id'])
                    member_name = member.name
                except User.DoesNotExist:
                    return Response({"error": "Membre non trouvé"}, status=404)
                except Exception as e:
                    logger.error(f"Erreur lors de la récupération du nom du membre: {str(e)}")

            # Créer la tâche d'équipe
            task = TeamTask(
                title=data['title'],
                description=data.get('description', ''),
                start_date=start_date,
                end_date=end_date,
                status='a_faire',
                priority=data.get('priority', 'moyenne'),
                team_id=data['team_id'],
                team_name=team.name,
                member_id=data.get('member_id', ""),
                member_name=member_name,
                responsable=data['responsable'],
                responsable_name=responsable.name,
                created_by=str(user.id),
                created_by_name=user.name,
                display_mode=data.get('display_mode', 'list')
            )
            task.save()

            return Response({
                "message": "Tâche d'équipe créée avec succès",
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "description": task.description,
                    "start_date": task.start_date,
                    "end_date": task.end_date,
                    "status": task.status,
                    "priority": task.priority,
                    "team_id": task.team_id,
                    "team_name": task.team_name,
                    "member_id": task.member_id,
                    "responsable": task.responsable,
                    "responsable_name": task.responsable_name,
                    "created_by": task.created_by,
                    "created_by_name": task.created_by_name,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at,
                    "display_mode": task.display_mode
                }
            }, status=201)

        except Exception as e:
            logger.error(f"Error in TeamTaskListCreateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la création de la tâche d'équipe"}, status=500)

    def get(self, request):
        """Liste des tâches d'équipe (filtrée selon le rôle et les permissions)"""
        try:
            user = request.user
            status_filter = request.query_params.get('status')

            # Filtrer les tâches selon le rôle de l'utilisateur
            if user.role == 'admin':
                # Les admins voient uniquement les tâches des équipes dont ils sont responsables
                admin_teams = Team.objects(responsable=str(user.id)).values_list('id')
                admin_team_ids = [str(team_id) for team_id in admin_teams]

                # Récupérer les membres de ces équipes
                team_members = []
                for team_id in admin_team_ids:
                    try:
                        team = Team.objects.get(id=team_id)
                        team_members.extend(list(team.members.keys()))
                    except Exception:
                        continue

                # Filtrer les tâches
                if admin_team_ids:
                    tasks = TeamTask.objects().filter(__raw__={'$or': [
                        {'team_id': {'$in': admin_team_ids}},
                        {'member_id': {'$in': team_members}}
                    ]})
                else:
                    # Si l'admin n'est responsable d'aucune équipe, il ne voit aucune tâche
                    tasks = TeamTask.objects().none()
            elif user.role == 'employee':
                # Les employés voient les tâches qui leur sont assignées ou assignées à leurs équipes
                user_id = str(user.id)

                # Récupérer les équipes dont l'utilisateur est membre
                # Méthode plus robuste pour éviter les problèmes de requête
                all_teams = Team.objects()
                user_teams = [team for team in all_teams if user_id in team.members]
                team_ids = [str(team.id) for team in user_teams]

                logger.debug(f"Employé {user_id} - Équipes trouvées: {len(user_teams)}")
                for team in user_teams:
                    logger.debug(f"  - {team.name} (ID: {team.id})")

                # Filtrer les tâches
                tasks = TeamTask.objects()
                if team_ids:
                    tasks = tasks.filter(__raw__={'$or': [
                        {'member_id': user_id},  # Tâches assignées spécifiquement à l'employé
                        {'$and': [               # Tâches assignées à toute l'équipe
                            {'team_id': {'$in': team_ids}},
                            {'$or': [
                                {'member_id': ''},
                                {'member_id': {'$exists': False}}
                            ]}
                        ]}
                    ]})
                else:
                    tasks = tasks.filter(member_id=user_id)
            else:
                # Les autres rôles n'ont pas accès aux tâches d'équipe
                return Response({"error": "Vous n'avez pas les droits pour voir les tâches d'équipe"}, status=403)

            # Filtrer par statut si spécifié
            if status_filter:
                tasks = tasks.filter(status=status_filter)

            # Formater les tâches
            tasks_data = []
            for task in tasks:
                # Déterminer l'assignation
                if task.member_id and task.member_id.strip():
                    # Tâche assignée à un membre spécifique
                    if task.member_name and task.member_name.strip():
                        member_display = task.member_name
                    else:
                        # Si le nom n'est pas stocké, essayer de le récupérer
                        try:
                            from ..mongo_models import User
                            member = User.objects.get(id=task.member_id)
                            member_display = member.name
                        except:
                            member_display = "Membre inconnu"
                else:
                    # Tâche assignée à toute l'équipe
                    member_display = "Toute l'équipe"

                tasks_data.append({
                    'id': str(task.id),
                    'title': task.title,
                    'description': task.description,
                    'start_date': task.start_date,
                    'end_date': task.end_date,
                    'status': task.status,
                    'priority': task.priority,
                    'team_id': task.team_id,
                    'team_name': task.team_name,
                    'member_id': task.member_id,
                    'member_name': member_display,
                    'responsable': task.responsable,
                    'responsable_name': task.responsable_name,
                    'created_by': task.created_by,
                    'created_by_name': task.created_by_name,
                    'created_at': task.created_at,
                    'updated_at': task.updated_at,
                    'display_mode': task.display_mode,
                    'can_manage': task.can_manage_task(user),
                    'can_update_status': task.can_update_status(user)
                })

            return Response(tasks_data)

        except Exception as e:
            logger.error(f"Error in TeamTaskListCreateView.get: {str(e)}")
            return Response({"error": str(e)}, status=500)

class TeamTaskDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, task_id):
        """Récupérer les détails d'une tâche d'équipe"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier les droits d'accès
            if user.role == 'admin':
                # Les admins ont accès uniquement aux tâches des équipes dont ils sont responsables
                if task.team_id:
                    try:
                        team = Team.objects.get(id=task.team_id)
                        if str(user.id) != team.responsable:
                            return Response({"error": "Vous n'avez pas les droits pour voir cette tâche. Seul l'administrateur responsable de l'équipe peut y accéder."}, status=403)
                    except Team.DoesNotExist:
                        return Response({"error": "Équipe non trouvée"}, status=404)
                else:
                    # Si la tâche n'est associée à aucune équipe, l'admin ne peut pas y accéder
                    return Response({"error": "Vous n'avez pas les droits pour voir cette tâche."}, status=403)
            elif user.role == 'employee':
                # Les employés ont accès aux tâches qui leur sont assignées ou assignées à leurs équipes
                user_id = str(user.id)

                # Vérifier si l'employé fait partie de l'équipe de la tâche
                if task.team_id:
                    team = Team.objects.get(id=task.team_id)
                    if user_id not in team.members:
                        return Response({"error": "Vous n'avez pas les droits pour voir cette tâche"}, status=403)

                # Si la tâche est assignée à un membre spécifique, vérifier que c'est bien cet employé
                if task.member_id and task.member_id.strip():
                    if task.member_id != user_id:
                        return Response({"error": "Vous n'avez pas les droits pour voir cette tâche"}, status=403)
                # Si member_id est vide, la tâche est assignée à toute l'équipe (déjà vérifié ci-dessus)
            else:
                # Les autres rôles n'ont pas accès aux tâches d'équipe
                return Response({"error": "Vous n'avez pas les droits pour voir les tâches d'équipe"}, status=403)

            # Retourner les détails de la tâche
            return Response({
                'id': str(task.id),
                'title': task.title,
                'description': task.description,
                'start_date': task.start_date,
                'end_date': task.end_date,
                'status': task.status,
                'priority': task.priority,
                'team_id': task.team_id,
                'team_name': task.team_name,
                'member_id': task.member_id,
                'responsable': task.responsable,
                'responsable_name': task.responsable_name,
                'created_by': task.created_by,
                'created_by_name': task.created_by_name,
                'created_at': task.created_at,
                'updated_at': task.updated_at,
                'display_mode': task.display_mode,
                'can_manage': task.can_manage_task(user),
                'can_update_status': task.can_update_status(user)
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.get: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la récupération des détails de la tâche d'équipe"}, status=500)

    @admin_required
    def put(self, request, task_id):
        """Modifier une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cette tâche
            if not task.can_manage_task(user):
                return Response({"error": "Vous n'êtes pas autorisé à modifier cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            data = request.data

            # Mettre à jour les champs
            if 'title' in data:
                # Vérifier si une autre tâche avec ce titre existe déjà pour la même équipe
                if data['title'] != task.title and TeamTask.objects(title=data['title'], team_id=task.team_id).first():
                    return Response({"error": "Une tâche avec ce titre existe déjà pour cette équipe"}, status=400)
                task.title = data['title']
            if 'description' in data:
                task.description = data['description']
            if 'start_date' in data:
                task.start_date = datetime.fromisoformat(data['start_date'].replace('Z', '+00:00'))
            if 'end_date' in data:
                task.end_date = datetime.fromisoformat(data['end_date'].replace('Z', '+00:00'))
            if 'priority' in data and data['priority'] in ['faible', 'moyenne', 'haute']:
                task.priority = data['priority']
            if 'team_id' in data:
                # Vérifier que l'admin est responsable de la nouvelle équipe
                try:
                    team = Team.objects.get(id=data['team_id'])
                    if team.responsable != str(user.id):
                        return Response({"error": "Vous n'êtes pas autorisé à assigner cette tâche à cette équipe. Seul l'administrateur responsable de l'équipe peut le faire."}, status=403)
                    task.team_id = data['team_id']
                    task.team_name = team.name
                except Team.DoesNotExist:
                    return Response({"error": "Équipe non trouvée"}, status=404)
            if 'member_id' in data:
                task.member_id = data['member_id']
                # Mettre à jour le nom du membre
                if data['member_id'] and data['member_id'].strip():
                    try:
                        from ..mongo_models import User
                        member = User.objects.get(id=data['member_id'])
                        task.member_name = member.name
                    except User.DoesNotExist:
                        return Response({"error": "Membre non trouvé"}, status=404)
                    except Exception as e:
                        logger.error(f"Erreur lors de la récupération du nom du membre: {str(e)}")
                        task.member_name = ""
                else:
                    task.member_name = ""
            if 'responsable' in data:
                # Vérifier que le nouveau responsable est un admin valide
                try:
                    responsable = User.objects.get(id=data['responsable'])
                    if responsable.role != 'admin':
                        return Response({"error": "Le responsable doit être un administrateur"}, status=400)
                    task.responsable = data['responsable']
                    task.responsable_name = responsable.name
                except User.DoesNotExist:
                    return Response({"error": "Responsable non trouvé"}, status=404)
            if 'display_mode' in data and data['display_mode'] in ['list', 'card', 'kanban']:
                task.display_mode = data['display_mode']

            # Sauvegarder les modifications
            task.save()

            return Response({
                "message": "Tâche d'équipe mise à jour avec succès",
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "description": task.description,
                    "start_date": task.start_date,
                    "end_date": task.end_date,
                    "status": task.status,
                    "priority": task.priority,
                    "team_id": task.team_id,
                    "team_name": task.team_name,
                    "member_id": task.member_id,
                    "responsable": task.responsable,
                    "responsable_name": task.responsable_name,
                    "created_by": task.created_by,
                    "created_by_name": task.created_by_name,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at,
                    "display_mode": task.display_mode
                }
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour de la tâche d'équipe"}, status=500)

    def patch(self, request, task_id):
        """Mettre à jour le statut d'une tâche d'équipe (membre assigné uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'utilisateur a le droit de mettre à jour le statut
            if not task.can_update_status(user):
                return Response({"error": "Vous n'êtes pas autorisé à mettre à jour le statut de cette tâche. Seul le membre assigné ou un membre de l'équipe peut le faire."}, status=403)

            data = request.data

            # Mettre à jour le statut
            if 'status' in data and data['status'] in ['a_faire', 'en_cours', 'en_revision', 'achevee']:
                task.status = data['status']
                task.save()

                return Response({
                    "message": "Statut de la tâche d'équipe mis à jour avec succès",
                    "status": task.status
                })
            else:
                return Response({"error": "Statut invalide. Les valeurs autorisées sont: a_faire, en_cours, en_revision, achevee"}, status=400)

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.patch: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour du statut de la tâche d'équipe"}, status=500)

    @admin_required
    def delete(self, request, task_id):
        """Supprimer une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cette tâche
            # ou si l'admin est celui qui a créé la tâche
            if not task.can_manage_task(user) and task.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à supprimer cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            # Supprimer la tâche
            task.delete()

            return Response({"message": "Tâche d'équipe supprimée avec succès"}, status=200)

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskDetailView.delete: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la suppression de la tâche d'équipe"}, status=500)

class TeamTaskArchiveView(APIView):
    permission_classes = [IsAuthenticated]

    @admin_required
    def put(self, request, task_id):
        """Archiver une tâche d'équipe (admin responsable de l'équipe uniquement)"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'admin a le droit de gérer cette tâche
            # ou si l'admin est celui qui a créé la tâche
            if not task.can_manage_task(user) and task.created_by != str(user.id):
                return Response({"error": "Vous n'êtes pas autorisé à archiver cette tâche. Seul l'administrateur responsable de l'équipe associée peut le faire."}, status=403)

            # Archiver la tâche
            task.status = 'archived'
            task.save()

            return Response({
                "message": "Tâche d'équipe archivée avec succès",
                "status": task.status
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskArchiveView.put: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de l'archivage de la tâche d'équipe"}, status=500)

class TeamTaskStatusUpdateView(APIView):
    """
    Vue pour mettre à jour le statut des tâches d'équipe avec des boutons d'action
    Permet aux membres assignés de changer facilement le statut
    """
    permission_classes = [IsAuthenticated]

    def post(self, request, task_id):
        """Mettre à jour le statut d'une tâche d'équipe avec des boutons d'action"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'utilisateur a le droit de mettre à jour le statut
            if not task.can_update_status(user):
                return Response({
                    "error": "Vous n'êtes pas autorisé à mettre à jour le statut de cette tâche",
                    "details": "Seul le membre assigné ou un membre de l'équipe peut modifier le statut"
                }, status=403)

            data = request.data
            new_status = data.get('status')

            # Valider le nouveau statut
            valid_statuses = ['a_faire', 'en_cours', 'en_revision', 'achevee']
            if new_status not in valid_statuses:
                return Response({
                    "error": "Statut invalide",
                    "valid_statuses": valid_statuses,
                    "received": new_status
                }, status=400)

            # Vérifier les transitions de statut autorisées
            current_status = task.status
            allowed_transitions = self._get_allowed_transitions(current_status)

            if new_status not in allowed_transitions:
                return Response({
                    "error": "Transition de statut non autorisée",
                    "current_status": current_status,
                    "new_status": new_status,
                    "allowed_transitions": allowed_transitions
                }, status=400)

            # Mettre à jour le statut
            old_status = task.status
            task.status = new_status
            task.save()

            # Déterminer l'assignation pour le message
            if task.member_id and task.member_id.strip():
                member_display = task.member_name or "Membre assigné"
            else:
                member_display = "Toute l'équipe"

            # Préparer la réponse avec les actions disponibles
            available_actions = self._get_available_actions(new_status)

            return Response({
                "message": f"Statut de la tâche mis à jour avec succès",
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "old_status": old_status,
                    "new_status": new_status,
                    "member_assigned": member_display,
                    "team_name": task.team_name,
                    "updated_at": task.updated_at.isoformat()
                },
                "available_actions": available_actions,
                "status_info": self._get_status_info(new_status)
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskStatusUpdateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour du statut"}, status=500)

    def get(self, request, task_id):
        """Récupérer les actions disponibles pour une tâche"""
        try:
            # Récupérer la tâche
            task = TeamTask.objects.get(id=task_id)
            user = request.user

            # Vérifier si l'utilisateur a le droit de voir les actions
            if not task.can_update_status(user):
                return Response({
                    "error": "Vous n'êtes pas autorisé à voir les actions de cette tâche"
                }, status=403)

            current_status = task.status
            available_actions = self._get_available_actions(current_status)
            allowed_transitions = self._get_allowed_transitions(current_status)

            return Response({
                "task": {
                    "id": str(task.id),
                    "title": task.title,
                    "current_status": current_status,
                    "member_assigned": task.member_name or "Toute l'équipe",
                    "team_name": task.team_name
                },
                "available_actions": available_actions,
                "allowed_transitions": allowed_transitions,
                "status_info": self._get_status_info(current_status),
                "can_update": True
            })

        except TeamTask.DoesNotExist:
            return Response({"error": "Tâche d'équipe non trouvée"}, status=404)
        except Exception as e:
            logger.error(f"Error in TeamTaskStatusUpdateView.get: {str(e)}")
            return Response({"error": "Une erreur est survenue"}, status=500)

    def _get_allowed_transitions(self, current_status):
        """Définit les transitions de statut autorisées"""
        transitions = {
            'a_faire': ['en_cours'],
            'en_cours': ['en_revision', 'achevee', 'a_faire'],
            'en_revision': ['en_cours', 'achevee'],
            'achevee': ['en_revision']  # Possibilité de rouvrir une tâche
        }
        return transitions.get(current_status, [])

    def _get_available_actions(self, current_status):
        """Retourne les boutons d'action disponibles selon le statut actuel"""
        actions = {
            'a_faire': [
                {
                    'action': 'start',
                    'label': 'Marquer comme en cours',
                    'new_status': 'en_cours',
                    'color': '#F59E0B',  # Jaune
                    'icon': 'play',
                    'description': 'Commencer à travailler sur cette tâche'
                }
            ],
            'en_cours': [
                {
                    'action': 'review',
                    'label': 'Marquer comme en révision',
                    'new_status': 'en_revision',
                    'color': '#8B5CF6',  # Violet
                    'icon': 'eye',
                    'description': 'Soumettre pour révision'
                },
                {
                    'action': 'complete',
                    'label': 'Marquer comme achevée',
                    'new_status': 'achevee',
                    'color': '#10B981',  # Vert
                    'icon': 'check',
                    'description': 'Marquer la tâche comme terminée'
                },
                {
                    'action': 'back_to_todo',
                    'label': 'Marquer comme à faire',
                    'new_status': 'a_faire',
                    'color': '#3B82F6',  # Bleu
                    'icon': 'arrow-left',
                    'description': 'Remettre en attente'
                }
            ],
            'en_revision': [
                {
                    'action': 'back_to_progress',
                    'label': 'Marquer comme en cours',
                    'new_status': 'en_cours',
                    'color': '#F59E0B',  # Jaune
                    'icon': 'edit',
                    'description': 'Reprendre le travail sur cette tâche'
                },
                {
                    'action': 'complete',
                    'label': 'Marquer comme achevée',
                    'new_status': 'achevee',
                    'color': '#10B981',  # Vert
                    'icon': 'check',
                    'description': 'Valider et terminer la tâche'
                }
            ],
            'achevee': [
                {
                    'action': 'reopen',
                    'label': 'Marquer comme en révision',
                    'new_status': 'en_revision',
                    'color': '#8B5CF6',  # Violet
                    'icon': 'refresh',
                    'description': 'Rouvrir pour révision'
                }
            ]
        }
        return actions.get(current_status, [])

    def _get_status_info(self, status):
        """Retourne les informations d'affichage pour un statut"""
        status_info = {
            'a_faire': {
                'label': 'À faire',
                'color': '#3B82F6',  # Bleu clair
                'background': '#EFF6FF',
                'icon': 'clock',
                'description': 'Tâche en attente de démarrage'
            },
            'en_cours': {
                'label': 'En cours',
                'color': '#F59E0B',  # Jaune
                'background': '#FFFBEB',
                'icon': 'play',
                'description': 'Tâche en cours de réalisation'
            },
            'en_revision': {
                'label': 'En révision',
                'color': '#8B5CF6',  # Violet
                'background': '#F3E8FF',
                'icon': 'eye',
                'description': 'Tâche en cours de révision'
            },
            'achevee': {
                'label': 'Achevée',
                'color': '#10B981',  # Vert foncé
                'background': '#ECFDF5',
                'icon': 'check',
                'description': 'Tâche terminée avec succès'
            }
        }
        return status_info.get(status, {})

class TeamTaskBulkStatusUpdateView(APIView):
    """
    Vue pour mettre à jour le statut de plusieurs tâches d'équipe en une fois
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Mettre à jour le statut de plusieurs tâches d'équipe"""
        try:
            data = request.data
            task_ids = data.get('task_ids', [])
            new_status = data.get('status')
            user = request.user

            if not task_ids:
                return Response({"error": "Aucune tâche spécifiée"}, status=400)

            if not new_status or new_status not in ['a_faire', 'en_cours', 'en_revision', 'achevee']:
                return Response({"error": "Statut invalide"}, status=400)

            updated_tasks = []
            failed_tasks = []

            for task_id in task_ids:
                try:
                    task = TeamTask.objects.get(id=task_id)

                    # Vérifier les permissions
                    if not task.can_update_status(user):
                        failed_tasks.append({
                            'task_id': task_id,
                            'title': task.title,
                            'error': 'Permission refusée'
                        })
                        continue

                    # Vérifier les transitions autorisées
                    allowed_transitions = self._get_allowed_transitions(task.status)
                    if new_status not in allowed_transitions:
                        failed_tasks.append({
                            'task_id': task_id,
                            'title': task.title,
                            'error': f'Transition non autorisée de {task.status} vers {new_status}'
                        })
                        continue

                    # Mettre à jour le statut
                    old_status = task.status
                    task.status = new_status
                    task.save()

                    updated_tasks.append({
                        'task_id': task_id,
                        'title': task.title,
                        'old_status': old_status,
                        'new_status': new_status
                    })

                except TeamTask.DoesNotExist:
                    failed_tasks.append({
                        'task_id': task_id,
                        'error': 'Tâche non trouvée'
                    })

            return Response({
                "message": f"{len(updated_tasks)} tâche(s) mise(s) à jour avec succès",
                "updated_tasks": updated_tasks,
                "failed_tasks": failed_tasks,
                "summary": {
                    "total_requested": len(task_ids),
                    "successful": len(updated_tasks),
                    "failed": len(failed_tasks)
                }
            })

        except Exception as e:
            logger.error(f"Error in TeamTaskBulkStatusUpdateView.post: {str(e)}")
            return Response({"error": "Une erreur est survenue lors de la mise à jour en lot"}, status=500)

    def _get_allowed_transitions(self, current_status):
        """Définit les transitions de statut autorisées (même logique que TeamTaskStatusUpdateView)"""
        transitions = {
            'a_faire': ['en_cours'],
            'en_cours': ['en_revision', 'achevee', 'a_faire'],
            'en_revision': ['en_cours', 'achevee'],
            'achevee': ['en_revision']
        }
        return transitions.get(current_status, [])